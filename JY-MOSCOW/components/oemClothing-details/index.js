// components/oemClothing-details/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    oemClothingInfo: {
      type: Object,
      value: {}
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    showImagePreview: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    showImg() {
      const oemClothingInfo = this.properties.oemClothingInfo;
      const images = oemClothingInfo.img || oemClothingInfo.oem_clothing_img || [];
      
      if (images.length > 0) {
        const imageUrls = images.map(img => {
          if (typeof img === 'string') {
            return img;
          } else if (img.url) {
            return img.url;
          }
          return '';
        }).filter(url => url);

        if (imageUrls.length > 0) {
          wx.previewImage({
            urls: imageUrls,
            current: imageUrls[0]
          });
        } else {
          wx.showToast({
            title: '暂无图片',
            icon: 'none'
          });
        }
      } else {
        wx.showToast({
          title: '暂无图片',
          icon: 'none'
        });
      }
    },

    // 计算进度百分比
    getProgressWidth() {
      const oemClothingInfo = this.properties.oemClothingInfo;
      const inPcs = oemClothingInfo.in_pcs || 0;
      const shipments = oemClothingInfo.shipments || 0;
      
      if (inPcs === 0) return 0;
      
      const percent = (shipments / inPcs) * 100;
      return Math.min(Math.max(percent, 0), 100);
    }
  },

  lifetimes: {
    attached() {
      // 组件初始化时可以做一些处理
    }
  }
});
