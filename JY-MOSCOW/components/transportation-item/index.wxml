<!-- components/transportation-item/index.wxml -->
<view class="container">
  <view class="index-number">{{detail.index + 1}}</view>
  
  <view class="item-list">
    <view 
      class="item" 
      wx:for="{{detail.bag}}" 
      wx:key="index"
      wx:for-item="item"
      data-clothing_id="{{item.clothing_id}}" 
      data-oem="{{item.oem}}" 
      data-item="{{item}}"
      bindtouchstart="touchStart" 
      bindtouchend="touchEnd"
    >
      <view class="clothing-name {{item.img && item.img.length > 0 ? 'has-image' : ''}}">
        {{item.clothing_name}}
      </view>
      <view class="out-pcs">{{item.out_pcs}} 件</view>
    </view>
  </view>
  
  <view class="package-info">
    {{detail.QUP}} * {{detail.package_quantity}} 包
  </view>
</view>
