<page-meta page-style="{{ show ? 'overflow: hidden;' : '' }}" />

<!-- 筛选区域 -->
<view class="filter-container">
  <picker bindchange="onChange1" value="{{value1}}" range="{{transportationCompanyList}}" range-key="text">
    <view class="dropdown-item">
      {{transportationCompanyList[value1] ? transportationCompanyList[value1].text : '选择货运公司'}}
    </view>
  </picker>

  <picker bindchange="onChange2" value="{{value2}}" range="{{option2}}" range-key="text">
    <view class="dropdown-item">
      {{option2[value2] ? option2[value2].text : '选择到货状态'}}
    </view>
  </picker>
</view>

<!-- 货运单列表 -->
<view class="list-container">
  <view wx:for="{{list}}" wx:key="index">
    <z-transportation
      bindtap='onGoToDetail'
      bind:myevent="onGetCode"
      data-item='{{item}}'
      transportation="{{item}}"
    >
      {{item.index}}
    </z-transportation>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading" hidden="{{!loadMore}}">
  <text>加载中...</text>
</view>
<view class="loading" hidden="{{!loadAll}}">
  <text>到底了...</text>
</view>

<!-- 详情弹窗 -->
<view wx:if="{{show}}" class="popup-overlay" bindtap="onClose">
  <view class="popup-content" catchtap="">
    <view class="popup-header">
      <view class="popup-title-section">
        <view class="popup-title">到货</view>
        <switch
          class="popup-switch"
          checked="{{checked}}"
          color="#07c160"
          bindchange="onChangeWitch"
        />
      </view>
      <button class="popup-button" bindtap="showImg">原图</button>
    </view>

    <view class="popup-body">
      <view wx:for="{{transportationDetail.detail}}" wx:key="index">
        <z-transportation-item
          bind:monitor="monitor"
          detail="{{item}}"
        ></z-transportation-item>
      </view>
    </view>
  </view>
</view>

<!-- 日期选择器弹窗 -->
<view wx:if="{{showDate}}" class="date-picker-overlay" bindtap="onCloseDatePopup">
  <view class="date-picker-content" catchtap="">
    <view class="date-picker-header">
      <text class="cancel-btn" bindtap="onCancel">取消</text>
      <text class="title">选择到货日期</text>
      <text class="confirm-btn" bindtap="onConfirm">确定</text>
    </view>

    <picker-view class="date-picker" value="{{currentDate}}" bindchange="onInput">
      <!-- 日期选择器实现 -->
    </picker-view>
  </view>
</view>

<!-- 服装信息弹窗 -->
<view wx:if="{{showClothingInfo}}" class="clothing-popup-overlay" bindtap="onCloseClothingInfo">
  <view class="clothing-popup-content" catchtap="">
    <view class="popup-clothing-info">
      <view wx:if="{{clothingInfo.clothing_id}}">
        <z-clothing-details clothingInfo="{{clothingInfo}}"></z-clothing-details>
      </view>
    </view>
    <view class="popup-clothing-info">
      <view wx:if="{{oemClothingInfo.oem_clothing_id}}">
        <z-oemClothing-details oemClothingInfo="{{oemClothingInfo}}"></z-oemClothing-details>
      </view>
    </view>
  </view>
</view>
