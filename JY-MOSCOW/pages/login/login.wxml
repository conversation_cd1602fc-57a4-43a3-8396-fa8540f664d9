<view class="bg-noLogin" wx:if="{{isLogin}}">
  <!-- 自动登录中的加载页面 -->
</view>

<view class="v1" wx:if="{{!isLogin}}">
  <!-- v2父容器  子view使用绝对布局 -->
  <view class="v2 {{loading ? 'loading' : ''}}">
    <view class="dltext">SAINGE</view>
    
    <!-- 微信昵称输入 -->
    <view class="phoneCs">
      <input 
        type="nickname" 
        class="weui-input" 
        bindblur="inpuWxNickName" 
        placeholder="      点击获取昵称"
        value="{{wxNickName}}"
        disabled="{{loading}}"
      />
    </view>
    
    <!-- 密码输入 -->
    <view class="passwordCs">
      <input 
        type="password" 
        class="weui-input" 
        bindinput="inputPassword" 
        placeholder="      请输入密码"
        value="{{userPwd}}"
        disabled="{{loading}}"
      />
    </view>
    
    <!-- 登录按钮 -->
    <view class="denglu">
      <button 
        class="btn-primary" 
        bindtap="onLogin"
        disabled="{{loading}}"
        loading="{{loading}}"
      >
        {{loading ? '登录中...' : '登录'}}
      </button>
    </view>
  </view>
</view>
