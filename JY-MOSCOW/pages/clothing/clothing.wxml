<!-- 搜索区域 -->
<view class="search-container">
  <view class="year-selector">
    <text class="year-label">年份：</text>
    <picker mode="selector" range="{{['2020', '2021', '2022', '2023', '2024', '2025']}}" value="{{year}}" bindchange="onChange">
      <view class="year-picker">{{year}}年</view>
    </picker>
  </view>
  
  <view class="search-box">
    <input 
      class="search-input" 
      placeholder="搜索款号" 
      value="{{search}}" 
      bindinput="onSearchInput"
      confirm-type="search"
      bindconfirm="onGoToSearch"
    />
    <button class="search-btn" bindtap="onGoToSearch" disabled="{{loading}}">
      {{loading ? '搜索中...' : '搜索'}}
    </button>
  </view>
  
  <view class="clear-btn" wx:if="{{clothingList.length > 0 || oemClothingList.length > 0}}" bindtap="onClearSearch">
    <text>清空</text>
  </view>
</view>

<!-- 普通服装列表 -->
<view wx:if="{{clothingList.length > 0}}" class="section">
  <view class="section-title">普通服装</view>
  <view wx:for="{{clothingList}}" wx:key="clothing_id" class="clothing-item">
    <view class="item-header">
      <view class="clothing-info">
        <text class="clothing-name">{{item.clothing_name}}</text>
        <text class="clothing-code">{{item.clothing_code}}</text>
      </view>
      <view class="price-container">
        <text class="price">¥{{item.clothing_price}}</text>
        <button class="price-edit-btn" bindtap="onChangePrice" data-item="{{item}}" data-type="normal">
          改价
        </button>
      </view>
    </view>
    
    <view class="item-content">
      <view class="progress-container">
        <view class="progress-info">
          <text class="progress-text">出货进度：{{item.shipments || 0}}/{{item.clipping_pcs || 0}}</text>
          <text class="progress-percent">{{item.percent.toFixed(1)}}%</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{item.percent}}%"></view>
        </view>
      </view>
      
      <view class="clothing-details">
        <view class="detail-row">
          <text class="label">裁剪件数：</text>
          <text class="value">{{item.clipping_pcs || 0}}</text>
        </view>
        <view class="detail-row">
          <text class="label">已出货：</text>
          <text class="value">{{item.shipments || 0}}</text>
        </view>
        <view class="detail-row" wx:if="{{item.clothing_year}}">
          <text class="label">年份：</text>
          <text class="value">{{item.clothing_year}}</text>
        </view>
      </view>
      
      <!-- 图片展示 -->
      <view wx:if="{{item.hasImages}}" class="image-container">
        <view class="image-list">
          <image 
            wx:for="{{item.imgList}}" 
            wx:key="index" 
            wx:for-item="imgUrl"
            class="clothing-image" 
            src="{{imgUrl}}" 
            mode="aspectFill"
            bindtap="onPreviewImage"
            data-images="{{item.imgList}}"
            data-index="{{index}}"
          />
        </view>
      </view>
    </view>
  </view>
</view>

<!-- OEM服装列表 -->
<view wx:if="{{oemClothingList.length > 0}}" class="section">
  <view class="section-title">OEM服装</view>
  <view wx:for="{{oemClothingList}}" wx:key="oem_clothing_id" class="clothing-item oem-item">
    <view class="item-header">
      <view class="clothing-info">
        <text class="clothing-name">{{item.oem_clothing_name}}</text>
        <text class="clothing-code">{{item.oem_clothing_code}}</text>
      </view>
      <view class="price-container">
        <text class="price">¥{{item.oem_clothing_price}}</text>
        <button class="price-edit-btn" bindtap="onChangePrice" data-item="{{item}}" data-type="oem">
          改价
        </button>
      </view>
    </view>
    
    <view class="item-content">
      <view class="progress-container">
        <view class="progress-info">
          <text class="progress-text">出货进度：{{item.shipments || 0}}/{{item.in_pcs || 0}}</text>
          <text class="progress-percent">{{item.percent.toFixed(1)}}%</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{item.percent}}%"></view>
        </view>
      </view>
      
      <view class="clothing-details">
        <view class="detail-row">
          <text class="label">入库件数：</text>
          <text class="value">{{item.in_pcs || 0}}</text>
        </view>
        <view class="detail-row">
          <text class="label">已出货：</text>
          <text class="value">{{item.shipments || 0}}</text>
        </view>
        <view class="detail-row" wx:if="{{item.oem_clothing_year}}">
          <text class="label">年份：</text>
          <text class="value">{{item.oem_clothing_year}}</text>
        </view>
      </view>
      
      <!-- 图片展示 -->
      <view wx:if="{{item.hasImages}}" class="image-container">
        <view class="image-list">
          <image 
            wx:for="{{item.imgList}}" 
            wx:key="index" 
            wx:for-item="imgUrl"
            class="clothing-image" 
            src="{{imgUrl}}" 
            mode="aspectFill"
            bindtap="onPreviewImage"
            data-images="{{item.imgList}}"
            data-index="{{index}}"
          />
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 空状态 -->
<view wx:if="{{clothingList.length === 0 && oemClothingList.length === 0 && !loading}}" class="empty-state">
  <text class="empty-text">暂无搜索结果</text>
  <text class="empty-hint">请输入款号进行搜索</text>
</view>

<!-- 加载状态 -->
<view wx:if="{{loading}}" class="loading-state">
  <text>搜索中...</text>
</view>
