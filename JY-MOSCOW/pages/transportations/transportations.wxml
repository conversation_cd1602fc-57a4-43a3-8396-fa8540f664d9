<page-meta page-style="{{ show ? 'overflow: hidden;' : '' }}" />

<!-- 筛选区域 -->
<view class="filter-container">
  <picker bindchange="onChange1" value="{{value1}}" range="{{transportationCompanyList}}" range-key="text">
    <view class="picker">
      {{transportationCompanyList[value1].text || '选择货运公司'}}
    </view>
  </picker>
  
  <picker bindchange="onChange2" value="{{value2}}" range="{{option2}}" range-key="text">
    <view class="picker">
      {{option2[value2].text || '选择到货状态'}}
    </view>
  </picker>
</view>

<!-- 货运单列表 -->
<view class="list-container">
  <view wx:for="{{list}}" wx:key="index" class="transportation-item" bindtap="onGoToDetail" data-item="{{item}}">
    <view class="item-header">
      <text class="transportation-id">{{item.transportation_id}}</text>
      <text class="transportation-company">{{item.transportation_company}}</text>
    </view>
    
    <view class="item-content">
      <view class="date-info">
        <text class="label">发货日期：</text>
        <text class="value">{{item.date_out}}</text>
      </view>
      
      <view class="date-info" wx:if="{{item.date_arrived}}">
        <text class="label">到货日期：</text>
        <text class="value">{{item.date_arrived}}</text>
      </view>
      
      <view class="date-info">
        <text class="label">运输天数：</text>
        <text class="value">{{item.days}}天</text>
      </view>
      
      <view class="date-info" wx:if="{{item.total_pcs}}">
        <text class="label">总件数：</text>
        <text class="value">{{item.total_pcs}}</text>
      </view>
    </view>
    
    <view class="item-status">
      <text class="status {{item.date_arrived ? 'arrived' : 'not-arrived'}}">
        {{item.date_arrived ? '已到货' : '未到货'}}
      </text>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading" hidden="{{!loadMore}}">
  <text>加载中...</text>
</view>
<view class="loading" hidden="{{!loadAll}}">
  <text>到底了...</text>
</view>

<!-- 详情弹窗 -->
<view class="popup-overlay" wx:if="{{show}}" bindtap="onClose">
  <view class="popup-content" catchtap="">
    <view class="popup-header">
      <text class="popup-title">货运单详情</text>
      <text class="close-btn" bindtap="onClose">×</text>
    </view>
    
    <view class="popup-body">
      <view class="detail-item">
        <text class="label">货运单号：</text>
        <text class="value">{{transportationDetail.transportation_id}}</text>
      </view>
      
      <view class="detail-item">
        <text class="label">货运公司：</text>
        <text class="value">{{transportationDetail.transportation_company}}</text>
      </view>
      
      <view class="detail-item">
        <text class="label">发货日期：</text>
        <text class="value">{{transportationDetail.date_out}}</text>
      </view>
      
      <view class="detail-item" wx:if="{{transportationDetail.date_arrived}}">
        <text class="label">到货日期：</text>
        <text class="value">{{transportationDetail.date_arrived}}</text>
      </view>
      
      <view class="switch-container">
        <text class="label">到货状态：</text>
        <switch checked="{{checked}}" bindchange="onChangeWitch" color="#07c160" />
      </view>
    </view>
  </view>
</view>

<!-- 日期选择器 -->
<view class="date-picker-overlay" wx:if="{{showDate}}" bindtap="onCloseDatePopup">
  <view class="date-picker-content" catchtap="">
    <view class="date-picker-header">
      <text class="cancel-btn" bindtap="onCancel">取消</text>
      <text class="title">选择到货日期</text>
      <text class="confirm-btn" bindtap="onConfirm">确定</text>
    </view>
    
    <picker-view class="date-picker" value="{{currentDate}}" bindchange="onInput">
      <!-- 这里可以添加日期选择器的具体实现 -->
    </picker-view>
  </view>
</view>
