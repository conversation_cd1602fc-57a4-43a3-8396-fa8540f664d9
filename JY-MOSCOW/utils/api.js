import http from "http.js"; // 引入封装好的请求方法

// ==================== 认证相关 API ====================

// 获取微信OpenID
const getOpenId = (params) => {
  return http({
    url: "/miniprogram/openid",
    method: "post",
    data: params,
  });
};

// 微信小程序登录
const login = (params) => {
  return http({
    url: "/miniprogram/login",
    method: "post",
    data: params,
  });
};

// 添加新用户
const addNewUser = (params) => {
  return http({
    url: "/miniprogram/operate",
    method: "post",
    data: params,
  });
};

// ==================== 货运相关 API ====================

// 获取货运单列表
const getTransportationList = (params) => {
  return http({
    url: "/miniprogram/transportationList",
    method: "get",
    data: params,
  });
};

// 获取货运公司列表
const getTransportationCompanyList = (params) => {
  return http({
    url: "/miniprogram/transportationCompanyList",
    method: "get",
    data: params,
  });
};

// 更新到货日期
const updateArrivedDate = (params) => {
  return http({
    url: "/miniprogram/updateArrivedDate",
    method: "post",
    data: params,
  });
};

// ==================== 服装相关 API ====================

// 获取普通服装信息
const getClothingInfo = (params) => {
  return http({
    url: "/miniprogram/clothingInfo",
    method: "get",
    data: params,
  });
};

// 获取OEM服装信息
const getOemClothingInfo = (params) => {
  return http({
    url: "/miniprogram/OemClothingInfo",
    method: "get",
    data: params,
  });
};

// 搜索普通服装
const searchClothing = (params) => {
  return http({
    url: "/miniprogram/searchClothing",
    method: "get",
    data: params,
  });
};

// 搜索OEM服装
const searchOemClothing = (params) => {
  return http({
    url: "/miniprogram/searchOemClothing",
    method: "get",
    data: params,
  });
};

// 修改价格
const changePrice = (params) => {
  return http({
    url: "/miniprogram/changePrice",
    method: "post",
    data: params,
  });
};

// 获取OEM服装图片列表
const getOemClothingImgList = (params) => {
  return http({
    url: "/miniprogram/oemClothingImgList",
    method: "get",
    data: params,
  });
};

// 获取普通服装图片列表
const getClothingImgList = (params) => {
  return http({
    url: "/miniprogram/clothingImgList",
    method: "get",
    data: params,
  });
};

// 将方法导出，实现复用
export default {
  // 认证相关
  login,
  addNewUser,
  getOpenId,
  
  // 货运相关
  getTransportationList,
  getTransportationCompanyList,
  updateArrivedDate,
  
  // 服装相关
  getClothingInfo,
  getOemClothingInfo,
  searchClothing,
  searchOemClothing,
  changePrice,
  getOemClothingImgList,
  getClothingImgList
};
