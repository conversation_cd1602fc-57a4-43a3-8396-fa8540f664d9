<!-- 搜索区域 -->
<view class="search-container">
  <view class="stepper-container">
    <view class="stepper-wrapper">
      <button class="stepper-btn" bindtap="decreaseYear">-</button>
      <view class="stepper-value">{{year}}</view>
      <button class="stepper-btn" bindtap="increaseYear">+</button>
    </view>
  </view>

  <view class="search-wrapper">
    <input
      class="search-input"
      placeholder="搜索款号"
      value="{{search}}"
      bindinput="onSearchInput"
      confirm-type="search"
      bindconfirm="onGoToSearch"
    />
    <button class="search-btn" bindtap="onGoToSearch" disabled="{{loading}}">
      搜索
    </button>
  </view>
</view>

<!-- 普通服装列表 -->
<view wx:for="{{clothingList}}" wx:key="index" class="clothing-item">
  <view>
    <z-clothing-details clothingInfo="{{item}}"></z-clothing-details>
  </view>
</view>

<!-- OEM服装列表 -->
<view wx:for="{{oemClothingList}}" wx:key="index" class="clothing-item">
  <view>
    <z-oemClothing-details oemClothingInfo="{{item}}"></z-oemClothing-details>
  </view>
</view>

<!-- 空状态 -->
<view wx:if="{{clothingList.length === 0 && oemClothingList.length === 0 && !loading}}" class="empty-state">
  <text class="empty-text">暂无搜索结果</text>
  <text class="empty-hint">请输入款号进行搜索</text>
</view>

<!-- 加载状态 -->
<view wx:if="{{loading}}" class="loading-state">
  <text>搜索中...</text>
</view>
