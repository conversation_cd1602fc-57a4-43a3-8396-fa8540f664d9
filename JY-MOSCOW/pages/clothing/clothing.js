// pages/clothing/clothing.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    search: "",
    year: "",
    clothingList: [],
    oemClothingList: [],
    loading: false,
    showImagePreview: false,
    previewImages: [],
    currentImageIndex: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const currentYear = new Date().getFullYear();
    this.setData({
      year: currentYear.toString(),
    });
  },

  /**
   * 搜索服装
   */
  async onGoToSearch() {
    if (this.data.search.trim() === "") {
      wx.showToast({
        title: "请输入搜索内容",
        icon: "none",
      });
      return;
    }

    this.setData({ loading: true });

    try {
      const params = {
        clothing_name: this.data.search,
        clothing_year: this.data.year,
      };

      // 搜索普通服装
      const clothingRes = await Api.searchClothing(params);
      let clothingList = [];

      if (clothingRes.data && clothingRes.data.data) {
        clothingList = clothingRes.data.data;
        clothingList.forEach((item) => {
          this.processClothingItem(item, 'normal');
        });
      }

      // 搜索OEM服装
      const oemClothingRes = await Api.searchOemClothing(params);
      let oemClothingList = [];

      if (oemClothingRes.data && oemClothingRes.data.data) {
        oemClothingList = oemClothingRes.data.data;
        oemClothingList.forEach((item) => {
          this.processClothingItem(item, 'oem');
        });
      }

      this.setData({
        clothingList: clothingList,
        oemClothingList: oemClothingList,
      });

      if (clothingList.length === 0 && oemClothingList.length === 0) {
        wx.showToast({
          title: "未找到相关服装",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("搜索服装失败:", error);
      wx.showToast({
        title: "搜索失败",
        icon: "none",
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 处理服装项数据
   */
  processClothingItem(item, type) {
    let imgList = [];
    let totalPcs, shipments;

    if (type === 'oem') {
      totalPcs = item.in_pcs || 0;
      shipments = item.shipments || 0;
    } else {
      totalPcs = item.clipping_pcs || 0;
      shipments = item.shipments || 0;
    }

    // 计算进度百分比
    const percent = totalPcs > 0 ? (shipments / totalPcs) * 100 : 0;
    item.percent = Math.min(Math.max(percent, 0), 100);
    item.percentWidth = Math.min(Math.max(percent * 5.5, 190), 550); // 用于进度条宽度

    // 处理图片
    if (item.clothing_img && Array.isArray(item.clothing_img)) {
      item.clothing_img.forEach((img) => {
        if (img && img.url) {
          imgList.push(img.url);
        }
      });
    } else if (item.oem_clothing_img && Array.isArray(item.oem_clothing_img)) {
      item.oem_clothing_img.forEach((img) => {
        if (img && img.url) {
          imgList.push(img.url);
        }
      });
    }

    item.imgList = imgList;
    item.hasImages = imgList.length > 0;
  },

  /**
   * 输入搜索内容
   */
  onSearchInput(e) {
    this.setData({
      search: e.detail.value,
    });
  },

  /**
   * 年份选择改变
   */
  onChange(e) {
    const years = ['2020', '2021', '2022', '2023', '2024', '2025'];
    const selectedYear = years[e.detail.value];
    this.setData({
      year: selectedYear,
    });
  },

  /**
   * 预览图片
   */
  onPreviewImage(e) {
    const { images, index } = e.currentTarget.dataset;
    if (images && images.length > 0) {
      wx.previewImage({
        urls: images,
        current: images[index || 0],
      });
    }
  },

  /**
   * 修改价格
   */
  async onChangePrice(e) {
    const { item, type } = e.currentTarget.dataset;

    wx.showModal({
      title: "修改价格",
      editable: true,
      placeholderText: "请输入新价格",
      success: async (res) => {
        if (res.confirm && res.content) {
          const newPrice = parseFloat(res.content);
          if (isNaN(newPrice) || newPrice < 0) {
            wx.showToast({
              title: "请输入有效价格",
              icon: "none",
            });
            return;
          }

          try {
            const params = {
              id: type === 'oem' ? item.oem_clothing_id : item.clothing_id,
              type: type === 'oem' ? 'oemClothing' : 'clothing',
              price: newPrice,
            };

            const result = await Api.changePrice(params);

            if (result.data && result.data.code === 200) {
              // 更新本地数据
              if (type === 'oem') {
                const updatedList = this.data.oemClothingList.map((clothing) => {
                  if (clothing.oem_clothing_id === item.oem_clothing_id) {
                    clothing.oem_clothing_price = newPrice;
                  }
                  return clothing;
                });
                this.setData({ oemClothingList: updatedList });
              } else {
                const updatedList = this.data.clothingList.map((clothing) => {
                  if (clothing.clothing_id === item.clothing_id) {
                    clothing.clothing_price = newPrice;
                  }
                  return clothing;
                });
                this.setData({ clothingList: updatedList });
              }

              wx.showToast({
                title: "价格修改成功",
                icon: "success",
              });
            } else {
              wx.showToast({
                title: "价格修改失败",
                icon: "none",
              });
            }
          } catch (error) {
            console.error("修改价格失败:", error);
            wx.showToast({
              title: "价格修改失败",
              icon: "none",
            });
          }
        }
      },
    });
  },

  /**
   * 清空搜索结果
   */
  onClearSearch() {
    this.setData({
      search: "",
      clothingList: [],
      oemClothingList: [],
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 可以在这里添加下拉刷新逻辑
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});
