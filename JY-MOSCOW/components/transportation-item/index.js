// components/transportation-item/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    detail: {
      type: Object,
      value: {}
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    touchStartTime: 0,
    longTapTimer: null
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onGoToDetail(e) {
      const { clothing_id, oem } = e.currentTarget.dataset;
      const item = e.currentTarget.dataset.item;
      
      // 触发父组件事件，传递服装详情信息
      this.triggerEvent('monitor', {
        clothing_id,
        oem,
        item
      });
    },

    touchStart(e) {
      this.setData({
        touchStartTime: Date.now()
      });
      
      // 设置长按定时器
      this.data.longTapTimer = setTimeout(() => {
        this.longtap(e);
      }, 600); // 600ms 长按
    },

    touchEnd(e) {
      // 清除长按定时器
      if (this.data.longTapTimer) {
        clearTimeout(this.data.longTapTimer);
        this.data.longTapTimer = null;
      }

      const touchEndTime = Date.now();
      const touchDuration = touchEndTime - this.data.touchStartTime;

      // 如果是短按（小于600ms），触发点击事件
      if (touchDuration < 600) {
        this.onGoToDetail(e);
      }
    },

    longtap(e) {
      // 长按事件处理
      const item = e.currentTarget.dataset.item;
      
      wx.showActionSheet({
        itemList: ['查看详情', '编辑信息'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 查看详情
            this.onGoToDetail(e);
          } else if (res.tapIndex === 1) {
            // 编辑信息
            this.triggerEvent('edit', { item });
          }
        }
      });
    }
  },
});
