/* 筛选区域 */
.filter-container {
  display: flex;
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.picker {
  flex: 1;
  padding: 20rpx;
  margin: 0 10rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}

/* 列表容器 */
.list-container {
  padding: 20rpx;
}

/* 货运单项 */
.transportation-item {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.transportation-id {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.transportation-company {
  font-size: 28rpx;
  color: #666;
}

.item-content {
  margin-bottom: 20rpx;
}

.date-info {
  display: flex;
  margin-bottom: 10rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.item-status {
  text-align: right;
}

.status {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.status.arrived {
  background-color: #07c160;
}

.status.not-arrived {
  background-color: #ff6b6b;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

/* 弹窗样式 */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-content {
  width: 90%;
  max-height: 80%;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #f5f5f5;
  border-bottom: 1rpx solid #eee;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-body {
  padding: 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.detail-item {
  display: flex;
  margin-bottom: 20rpx;
}

.detail-item .label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}

.detail-item .value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.switch-container {
  display: flex;
  align-items: center;
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #eee;
}

.switch-container .label {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

/* 日期选择器 */
.date-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  display: flex;
  align-items: flex-end;
}

.date-picker-content {
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
}

.date-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.cancel-btn,
.confirm-btn {
  font-size: 28rpx;
  color: #007aff;
}

.date-picker-header .title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.date-picker {
  height: 400rpx;
}
