/* 筛选区域 */
.filter-container {
  display: flex;
  padding: 20rpx;
  background-color: #356363;
  gap: 20rpx;
}

.dropdown-item {
  flex: 1;
  padding: 20rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  position: relative;
}

.dropdown-item::after {
  content: '▼';
  position: absolute;
  right: 15rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20rpx;
  color: #666;
}

/* 列表容器 */
.list-container {
  background-color: #f5f5f5;
  min-height: calc(100vh - 120rpx);
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

/* 弹窗样式 */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0);
  z-index: 1000;
}

.popup-content {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.popup-header {
  padding: 30rpx;
  background-color: #356363;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.popup-title-section {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.popup-switch {
  transform: scale(1.2);
}

.popup-button {
  padding: 15rpx 30rpx;
  background-color: #07c160;
  color: #fff;
  border-radius: 25rpx;
  font-size: 24rpx;
  border: none;
}

.popup-body {
  flex: 1;
  overflow-y: auto;
  background-color: #f5f5f5;
}

/* 日期选择器 */
.date-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  display: flex;
  align-items: flex-end;
}

.date-picker-content {
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
}

.date-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.cancel-btn,
.confirm-btn {
  font-size: 28rpx;
  color: #007aff;
}

.date-picker-header .title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.date-picker {
  height: 400rpx;
}

/* 服装信息弹窗 */
.clothing-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1002;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clothing-popup-content {
  width: 90%;
  max-height: 80%;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  overflow: hidden;
}

.popup-clothing-info {
  max-height: 400rpx;
  overflow-y: auto;
}
