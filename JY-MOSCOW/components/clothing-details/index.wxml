<!-- components/clothing-details/index.wxml -->
<view class="popup">
  <view class="card">
    <view class="card-box">
      <view class="card-box-content">
        <view class="card-content-a">{{clothingInfo.clothing_name}}</view>
        <view class="card-content-b">
          <view wx:if="{{clothingInfo.clothing_price}}" class="price-tag">
            ￥{{clothingInfo.clothing_price}}
          </view>
        </view>
        <button 
          wx:if="{{clothingInfo.img && clothingInfo.img.length > 0}}" 
          bindtap="showImg" 
          class="image-btn"
        >
          图片
        </button>
      </view>
      
      <view class="card-box-card-tag">
        <view wx:if="{{clothingInfo.supplier}}" class="tag supplier-tag">
          {{clothingInfo.supplier}}
        </view>
        <view 
          wx:if="{{clothingInfo.group_classification}}" 
          wx:for="{{clothingInfo.group_classification}}" 
          wx:key="index" 
          class="tag classification-tag"
        >
          {{item}}
        </view>
      </view>
      
      <view class="card-box-card-tag">
        <view wx:if="{{clothingInfo.long_or_short_sleeve}}" class="tag sleeve-tag">
          {{clothingInfo.long_or_short_sleeve}}
        </view>
        <view wx:if="{{clothingInfo.size}}" class="tag size-tag">
          {{clothingInfo.size}}
        </view>
        <view wx:if="{{clothingInfo.style}}" class="tag style-tag">
          {{clothingInfo.style}}
        </view>
        <view wx:if="{{clothingInfo.pocket_type}}" class="tag pocket-tag">
          {{clothingInfo.pocket_type}}
        </view>
      </view>
    </view>
  </view>
  
  <view class="card-clipping_pcs">
    <view class="label">裁剪数</view>
    <view class="value">{{clothingInfo.clipping_pcs || 0}}</view>
  </view>
  
  <view 
    wx:if="{{clothingInfo.shipments !== 0 && clothingInfo.shipments !== null}}" 
    class="card-shipments"
  >
    <view class="label">发货数</view>
    <view class="value">{{clothingInfo.shipments || 0}}</view>
    <view class="progress-bar">
      <view 
        class="progress-fill" 
        style="width: {{(clothingInfo.shipments / clothingInfo.clipping_pcs * 100) || 0}}%"
      ></view>
    </view>
  </view>
</view>
