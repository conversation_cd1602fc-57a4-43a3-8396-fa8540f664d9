// 简单的 API 测试脚本
// 在微信开发者工具的控制台中运行

const API_BASE = 'http://localhost:4000/api';

// 测试获取货运公司列表（不需要认证）
async function testGetCompanyList() {
  try {
    const response = await fetch(`${API_BASE}/miniprogram/transportationCompanyList`);
    const data = await response.json();
    console.log('货运公司列表:', data);
    return data;
  } catch (error) {
    console.error('获取货运公司列表失败:', error);
  }
}

// 测试登录接口
async function testLogin() {
  try {
    const loginData = {
      userName: 'admin',
      userPwd: 'admin123',
      wxOpenId: 'test_openid_123'
    };
    
    const response = await fetch(`${API_BASE}/miniprogram/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(loginData)
    });
    
    const data = await response.json();
    console.log('登录结果:', data);
    
    if (data.code === 200 && data.data && data.data.token) {
      // 保存 token 用于后续测试
      localStorage.setItem('test_token', data.data.token);
      console.log('Token 已保存:', data.data.token);
    }
    
    return data;
  } catch (error) {
    console.error('登录失败:', error);
  }
}

// 测试需要认证的接口（货运单列表）
async function testTransportationList() {
  try {
    const token = localStorage.getItem('test_token');
    if (!token) {
      console.error('请先登录获取 token');
      return;
    }
    
    const response = await fetch(`${API_BASE}/miniprogram/transportationList?page=1&limit=5`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const data = await response.json();
    console.log('货运单列表:', data);
    return data;
  } catch (error) {
    console.error('获取货运单列表失败:', error);
  }
}

// 测试服装搜索接口
async function testSearchClothing() {
  try {
    const token = localStorage.getItem('test_token');
    if (!token) {
      console.error('请先登录获取 token');
      return;
    }
    
    const response = await fetch(`${API_BASE}/miniprogram/searchClothing?clothing_name=测试`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const data = await response.json();
    console.log('服装搜索结果:', data);
    return data;
  } catch (error) {
    console.error('搜索服装失败:', error);
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('=== 开始 API 测试 ===');
  
  console.log('\n1. 测试货运公司列表（无需认证）');
  await testGetCompanyList();
  
  console.log('\n2. 测试登录接口');
  await testLogin();
  
  // 等待一秒确保 token 保存
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  console.log('\n3. 测试货运单列表（需要认证）');
  await testTransportationList();
  
  console.log('\n4. 测试服装搜索（需要认证）');
  await testSearchClothing();
  
  console.log('\n=== API 测试完成 ===');
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testGetCompanyList,
    testLogin,
    testTransportationList,
    testSearchClothing,
    runAllTests
  };
}

// 如果在浏览器环境中，将函数添加到全局对象
if (typeof window !== 'undefined') {
  window.apiTest = {
    testGetCompanyList,
    testLogin,
    testTransportationList,
    testSearchClothing,
    runAllTests
  };
  
  console.log('API 测试函数已加载到 window.apiTest');
  console.log('运行 window.apiTest.runAllTests() 开始测试');
}
