import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { UsersModule } from './modules/users/users.module'
import { AuthModule } from './modules/auth/auth.module'
import { DatabaseModule } from './database/database.module'
import { FabricsModule } from './modules/fabric/fabric.module'
import { FabricWarehouseModule } from './modules/fabricWarehouse/fabricWarehouse.module'
import { StaffModule } from './modules/staff/staff.module'
import { ClothingModule } from './modules/clothing/clothing.module'
import { FabricGroupModule } from './modules/fabricGroup/fabricGroup.module'
import { OemClothingModule } from './modules/oemClothing/oem-clothing.module'
import { WorkModule } from './modules/work/work.module'
import { OemClothingIncomingModule } from './modules/oemClothingIncoming/oem-clothing-incoming.module'
import { TransportationModule } from './modules/transportation/transportation.module'
import { DivisionWorkModule } from './modules/divisionWork/division-work.module'
import { DivisionWorkAssignModule } from './modules/divisionWorkAssign/division-work-assign.module'
import { DivisionWorkCompleteModule } from './modules/divisionWorkComplete/division-work-complete.module'
import { UploadModule } from './modules/upload/upload.module'
import { MiniProgramModule } from './modules/miniProgram/mini-program.module'

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    DatabaseModule,
    UsersModule,
    AuthModule,
    FabricsModule,
    FabricWarehouseModule,
    StaffModule,
    ClothingModule,
    FabricGroupModule,
    OemClothingModule,
    WorkModule,
    OemClothingIncomingModule,
    TransportationModule,
    DivisionWorkModule,
    DivisionWorkAssignModule,
    DivisionWorkCompleteModule,
    UploadModule,
    MiniProgramModule,
  ],
})
export class AppModule {}
