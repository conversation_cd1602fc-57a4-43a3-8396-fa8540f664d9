/* 最大的父元素 */
.v1 {
  display: flex;
  justify-content: center;
  position: absolute;
  height: 100%;
  width: 100%;
  background: #35636370;
}

/* 白色区域 */
.v1 .v2 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 250rpx;
  width: 545rpx;
  height: 500rpx;
  background: #ffffff;
  box-shadow: 0 10px 6px -6px rgba(30, 30, 30, 0.1),
    12px 0 8px -8px rgba(50, 50, 50, 0.1);
  border-radius: 50rpx;
}

/* 白色区域内的登录文本 */
.dltext {
  font-size: 50rpx;
  font-family: Helvetica;
  color: #000000;
}

/* 手机号输入框 */
.phoneCs {
  margin-top: 30rpx;
  width: 300rpx;
  font-size: 30rpx;
}

.passwordCs {
  margin-top: 15rpx;
  margin-bottom: 30rpx;
  width: 300rpx;
  font-size: 30rpx;
}

/* 登录按钮容器view */
.denglu {
  width: 270rpx;
  height: 60rpx;
}

/* 登录按钮 */
.denglu button {
  padding: 0rpx;
  line-height: 50rpx;
  font-size: 35rpx;
  width: 100%;
  height: 100%;
  border-radius: 30rpx;
}

.bg-noLogin {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}
