<!-- components/oemClothing-details/index.wxml -->
<view class="popup">
  <view class="card">
    <view class="card-box">
      <view class="card-box-content">
        <view class="card-content-a">{{oemClothingInfo.oem_clothing_name}}</view>
        <view class="card-content-b">
          <view wx:if="{{oemClothingInfo.price || oemClothingInfo.oem_clothing_price}}" class="price-tag">
            ￥{{oemClothingInfo.price || oemClothingInfo.oem_clothing_price}}
          </view>
        </view>
        <button 
          wx:if="{{oemClothingInfo.img && oemClothingInfo.img.length > 0}}" 
          bindtap="showImg" 
          class="image-btn"
        >
          图片
        </button>
      </view>
      
      <view class="card-box-card-tag">
        <view wx:if="{{oemClothingInfo.supplier}}" class="tag supplier-tag">
          {{oemClothingInfo.supplier}}
        </view>
        <view 
          wx:if="{{oemClothingInfo.group_classification}}" 
          wx:for="{{oemClothingInfo.group_classification}}" 
          wx:key="index" 
          class="tag classification-tag"
        >
          {{item}}
        </view>
      </view>
      
      <view class="card-box-card-tag">
        <view wx:if="{{oemClothingInfo.two}}" class="tag two-tag">
          {{oemClothingInfo.two}}
        </view>
        <view wx:if="{{oemClothingInfo.size}}" class="tag size-tag">
          {{oemClothingInfo.size}}
        </view>
        <view wx:if="{{oemClothingInfo.style}}" class="tag style-tag">
          {{oemClothingInfo.style}}
        </view>
      </view>
    </view>
  </view>
  
  <view class="card-in_pcs">
    <view class="label">入库数</view>
    <view class="value">{{oemClothingInfo.in_pcs || 0}}</view>
  </view>
  
  <view 
    wx:if="{{oemClothingInfo.shipments !== 0 && oemClothingInfo.shipments !== null}}" 
    class="card-shipments"
  >
    <view class="label">发货数</view>
    <view class="value">{{oemClothingInfo.shipments || 0}}</view>
    <view class="progress-bar">
      <view 
        class="progress-fill" 
        style="width: {{(oemClothingInfo.shipments / oemClothingInfo.in_pcs * 100) || 0}}%"
      ></view>
    </view>
  </view>
</view>
