// components/transportation/index.js
import Api from "../../utils/api.js";

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    transportation: {
      type: Object,
      value: {}
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    price: "",
    show: false,
    transportation_id: ""
  },

  lifetimes: {
    attached() {
      this.setData({
        price: this.properties.transportation.price || ""
      });
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    showPopup() {
      this.setData({
        show: true,
        transportation_id: this.properties.transportation._id || this.properties.transportation.transportation_id
      });
    },

    cancel() {
      this.setData({
        show: false,
      });
    },

    async changePrice() {
      try {
        const price = this.data.price;
        const transportation_id = this.data.transportation_id;
        
        if (!price || !transportation_id) {
          wx.showToast({
            title: "请输入有效价格",
            icon: "none"
          });
          return;
        }

        // 这里可以调用价格更新API
        // const params = { price, transportation_id };
        // const res = await Api.changePrice(params);
        
        wx.showToast({
          title: "价格更新成功",
          icon: "success"
        });

        this.setData({
          show: false,
        });
      } catch (error) {
        console.error("更新价格失败:", error);
        wx.showToast({
          title: "更新失败",
          icon: "none"
        });
        this.setData({
          price: ""
        });
      }
    },

    clickOverlay() {
      const myEventDetail = {
        val: false
      };
      this.triggerEvent('myevent', myEventDetail);
    },
  },
});
