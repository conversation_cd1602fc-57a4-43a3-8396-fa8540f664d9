<!-- components/transportation/index.wxml -->
<view class="card">
  <view class="transportation">
    <view class="detail">
      <view class="a">
        <view class="supplier">{{transportation.supplier || transportation.transportation_company}}</view>
        <view class="b">
          <view wx:if="{{transportation.weight}}" class="weight-tag">
            {{transportation.weight}} kg
          </view>
          <view wx:if="{{price}}" class="price-tag">
            $ {{price}}
          </view>
          <view catchtap="showPopup" wx:if="{{!price}}" class="price-tag clickable">
            单价
          </view>
        </view>
      </view>
      <view class="total_package_quantity">
        {{transportation.total_package_quantity || transportation.total_pcs || 0}}包
      </view>
    </view>
    
    <view class="date">
      <view class="date-tag out-date">{{transportation.date_out}}</view>
      <view class="date-tag days">{{transportation.days}}天</view>
      <view wx:if="{{transportation.date_arrived}}" class="date-tag arrived-date">
        {{transportation.date_arrived}}
      </view>
    </view>
  </view>
</view>

<!-- 价格输入弹窗 -->
<view wx:if="{{show}}" class="popup-overlay" bindtap="clickOverlay">
  <view class="popup-price" catchtap="">
    <view class="popup-header">
      <text class="popup-title">设置运费单价</text>
    </view>
    
    <view class="popup-body">
      <input 
        class="input-price" 
        type="number" 
        value="{{price}}" 
        bindinput="{{(e) => setData({price: e.detail.value})}}"
        placeholder="请输入运费单价" 
        placeholder-class="input-placeholder"
      />
    </view>
    
    <view class="popup-footer">
      <button class="cancel-btn" catchtap="cancel">取消</button>
      <button class="confirm-btn" catchtap="changePrice">确定</button>
    </view>
  </view>
</view>
