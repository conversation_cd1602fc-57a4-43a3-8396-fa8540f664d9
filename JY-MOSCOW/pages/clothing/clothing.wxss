/* 搜索区域 */
.search-container {
  background-color: #356363;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.stepper-container {
  display: flex;
  justify-content: center;
}

.stepper-wrapper {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 30rpx;
  overflow: hidden;
}

.stepper-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #356363;
  color: #fff;
  border: none;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stepper-value {
  width: 100rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  background-color: #fff;
}

.search-wrapper {
  display: flex;
  gap: 20rpx;
}

.search-input {
  flex: 1;
  padding: 20rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
}

.search-btn {
  padding: 20rpx 30rpx;
  background-color: #07c160;
  color: #fff;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.search-btn[disabled] {
  background-color: rgba(7, 193, 96, 0.5);
}

/* 服装项 */
.clothing-item {
  /* 组件内部已有样式，这里不需要额外样式 */
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.empty-hint {
  font-size: 24rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60rpx;
  color: #999;
  font-size: 28rpx;
}
