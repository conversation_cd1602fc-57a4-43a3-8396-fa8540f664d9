import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { Transportation } from '../../models/transportation.model'
import { TransportationDetail } from '../../models/transportationDetail.model'
import { Clothing } from '../../models/clothing.model'
import { OemClothing } from '../../models/oemClothing.model'
import {
  CreateTransportationDto,
  UpdateTransportationDto,
  QueryTransportationDto,
  CreateTransportationDetailDto,
  UpdateTransportationDetailDto,
} from './dto'

@Injectable()
export class TransportationService {
  constructor(
    @InjectModel('Transportation')
    private readonly transportationModel: Model<Transportation>,
    @InjectModel('TransportationDetail')
    private readonly transportationDetailModel: Model<TransportationDetail>,
    @InjectModel('Clothing')
    private readonly clothingModel: Model<Clothing>,
    @InjectModel('OemClothing')
    private readonly oemClothingModel: Model<OemClothing>
  ) {}

  // 创建发货信息
  async create(createTransportationDto: CreateTransportationDto): Promise<Transportation> {
    const transportation = new this.transportationModel(createTransportationDto)
    return transportation.save()
  }

  // 获取发货信息列表，支持分页和筛选
  async findAll(queryParams: QueryTransportationDto) {
    console.log('后端收到的查询参数：', JSON.stringify(queryParams))

    // 处理可能的数组参数格式问题
    const processedParams = { ...queryParams } as Record<string, any>

    // 检查并处理 transportation_years[] 格式的参数
    for (const key in queryParams as Record<string, any>) {
      if (key.endsWith('[]')) {
        const baseKey = key.slice(0, -2)
        processedParams[baseKey] = (queryParams as Record<string, any>)[key]
        delete processedParams[key]
      }
    }

    const {
      transportation_id,
      transportation_year,
      transportation_years,
      date_out_start,
      date_out_end,
      supplier,
      suppliers,
      page = 1,
      limit = 10,
    } = processedParams

    // 构建查询条件
    const filter: any = {}

    if (transportation_id) {
      filter.transportation_id = { $regex: transportation_id, $options: 'i' }
    }

    if (transportation_year) {
      filter.transportation_year = transportation_year
    } else if (transportation_years && transportation_years.length > 0) {
      filter.transportation_year = { $in: transportation_years }
    }

    if (date_out_start && date_out_end) {
      filter.date_out = {
        $gte: new Date(date_out_start),
        $lte: new Date(date_out_end),
      }
    } else if (date_out_start) {
      filter.date_out = { $gte: new Date(date_out_start) }
    } else if (date_out_end) {
      filter.date_out = { $lte: new Date(date_out_end) }
    }

    if (supplier) {
      filter.supplier = { $regex: supplier, $options: 'i' }
    } else if (suppliers && suppliers.length > 0) {
      filter.supplier = { $in: suppliers }
    }

    console.log('构建的数据库查询条件：', JSON.stringify(filter))

    // 计算分页参数
    const skip = (Number(page) - 1) * Number(limit)

    // 执行查询
    const [data, total] = await Promise.all([
      this.transportationModel
        .find(filter)
        .skip(skip)
        .limit(Number(limit))
        .sort({ date_out: -1 }) // 按发货日期倒序排列
        .exec(),
      this.transportationModel.countDocuments(filter).exec(),
    ])

    console.log(`查询结果：找到 ${total} 条记录`)
    return {
      total,
      page: Number(page),
      limit: Number(limit),
      data,
    }
  }

  // 获取单个发货信息
  async findOne(id: string): Promise<Transportation> {
    const transportation = await this.transportationModel.findById(id).exec()
    if (!transportation) {
      throw new NotFoundException(`发货信息ID ${id} 不存在`)
    }
    return transportation
  }

  // 更新发货信息
  async update(
    id: string,
    updateTransportationDto: UpdateTransportationDto,
    details?: Omit<CreateTransportationDetailDto, 'transportation_id'>[]
  ): Promise<Transportation> {
    try {
      // 1. 更新发货信息主表
      const updatedTransportation = await this.transportationModel
        .findByIdAndUpdate(id, updateTransportationDto, { new: true })
        .exec()

      if (!updatedTransportation) {
        throw new NotFoundException(`发货信息ID ${id} 不存在`)
      }

      // 如果没有提供明细数据，则只更新主表信息
      if (!details || details.length === 0) {
        return updatedTransportation
      }

      // 2. 查找所有相关的发货明细，收集服装ID
      const existingDetails = await this.transportationDetailModel
        .find({ transportation_id: id })
        .exec()

      console.log(`找到 ${existingDetails.length} 条相关的发货明细记录`)

      // 3. 收集所有服装ID，用于更新发货数量
      const clothingIds = new Set<string>()
      const oemClothingIds = new Set<string>()

      // 收集现有明细中的服装ID
      for (const detail of existingDetails) {
        if (detail.oem === '是') {
          oemClothingIds.add(detail.clothing_id)
        } else {
          clothingIds.add(detail.clothing_id)
        }
      }

      // 4. 收集新明细中的服装ID
      for (const detail of details) {
        if (detail.oem === '是') {
          oemClothingIds.add(detail.clothing_id)
        } else {
          clothingIds.add(detail.clothing_id)
        }
      }

      console.log(`需要更新的普通服装数量: ${clothingIds.size}, OEM服装数量: ${oemClothingIds.size}`)

      // 5. 删除旧的发货明细
      const deleteDetailsResult = await this.transportationDetailModel
        .deleteMany({ transportation_id: id })
        .exec()

      console.log(`已删除 ${deleteDetailsResult.deletedCount} 条发货明细记录`)

      // 6. 新增新的发货明细
      const detailsToInsert = details.map((detail) => ({
        ...detail,
        transportation_id: id,
      }))

      await this.transportationDetailModel.insertMany(detailsToInsert)
      console.log(`已新增 ${detailsToInsert.length} 条发货明细记录`)

      // 7. 更新所有相关服装的发货数量
      for (const clothingId of clothingIds) {
        await this.updateClothingShipments(clothingId, '否')
      }

      for (const oemClothingId of oemClothingIds) {
        await this.updateClothingShipments(oemClothingId, '是')
      }

      // 8. 更新发货信息的总数量
      await this.updateTransportationTotals(id)

      return updatedTransportation
    } catch (error) {
      console.error('更新发货信息时出错:', error)
      throw error
    }
  }

  // 删除发货信息
  async remove(id: string): Promise<{ success: boolean; message: string }> {
    try {
      // 1. 查找发货信息
      const transportation = await this.transportationModel.findById(id).exec()
      if (!transportation) {
        throw new NotFoundException(`发货信息ID ${id} 不存在`)
      }

      // 2. 查找所有相关的发货明细，收集服装ID
      const details = await this.transportationDetailModel.find({ transportation_id: id }).exec()
      console.log(`找到 ${details.length} 条相关的发货明细记录`)

      // 收集所有服装ID，用于更新发货数量
      const clothingIds = new Set<string>()
      const oemClothingIds = new Set<string>()

      for (const detail of details) {
        if (detail.oem === '是') {
          oemClothingIds.add(detail.clothing_id)
        } else {
          clothingIds.add(detail.clothing_id)
        }
      }

      console.log(`需要更新的普通服装数量: ${clothingIds.size}, OEM服装数量: ${oemClothingIds.size}`)

      // 3. 删除相关的发货明细
      const deleteDetailsResult = await this.transportationDetailModel
        .deleteMany({ transportation_id: id })
        .exec()
      console.log(`已删除 ${deleteDetailsResult.deletedCount} 条发货明细记录`)

      // 4. 删除发货信息
      await this.transportationModel.findByIdAndDelete(id).exec()

      // 5. 更新所有相关服装的发货数量
      for (const clothingId of clothingIds) {
        await this.updateClothingShipments(clothingId, '否')
      }

      for (const oemClothingId of oemClothingIds) {
        await this.updateClothingShipments(oemClothingId, '是')
      }

      return {
        success: true,
        message: `发货信息及其 ${deleteDetailsResult.deletedCount} 条明细记录已成功删除，并已更新相关服装的出货数量`,
      }
    } catch (error) {
      console.error('删除发货信息时出错:', error)
      throw error
    }
  }

  // 创建发货明细
  async createDetail(
    createTransportationDetailDto: CreateTransportationDetailDto
  ): Promise<TransportationDetail> {
    try {
      // 检查发货信息是否存在
      const transportation = await this.transportationModel
        .findById(createTransportationDetailDto.transportation_id)
        .exec()
      if (!transportation) {
        throw new NotFoundException(
          `发货信息ID ${createTransportationDetailDto.transportation_id} 不存在`
        )
      }

      // 创建发货明细
      const detail = new this.transportationDetailModel(createTransportationDetailDto)
      const savedDetail = await detail.save()

      // 更新服装的发货数量
      await this.updateClothingShipments(
        createTransportationDetailDto.clothing_id,
        createTransportationDetailDto.oem
      )

      return savedDetail
    } catch (error) {
      console.error('创建发货明细时出错:', error)
      throw error
    }
  }

  // 批量创建发货明细
  async createDetailBatch(
    transportationId: string,
    details: Omit<CreateTransportationDetailDto, 'transportation_id'>[]
  ): Promise<TransportationDetail[]> {
    try {
      // 检查发货信息是否存在
      const transportation = await this.transportationModel.findById(transportationId).exec()
      if (!transportation) {
        throw new NotFoundException(`发货信息ID ${transportationId} 不存在`)
      }

      // 准备批量插入的数据
      const detailsToInsert = details.map((detail) => ({
        ...detail,
        transportation_id: transportationId,
      }))

      // 批量插入发货明细
      const savedDetails = await this.transportationDetailModel.insertMany(detailsToInsert)

      // 收集所有服装ID，用于更新发货数量
      const clothingIds = new Set<string>()
      const oemClothingIds = new Set<string>()

      for (const detail of detailsToInsert) {
        if (detail.oem === '是') {
          oemClothingIds.add(detail.clothing_id)
        } else {
          clothingIds.add(detail.clothing_id)
        }
      }

      // 更新服装的发货数量
      for (const clothingId of clothingIds) {
        await this.updateClothingShipments(clothingId, '否')
      }

      // 更新OEM服装的发货数量
      for (const oemClothingId of oemClothingIds) {
        await this.updateClothingShipments(oemClothingId, '是')
      }

      // 更新发货信息的总数量
      await this.updateTransportationTotals(transportationId)

      return savedDetails
    } catch (error) {
      console.error('批量创建发货明细时出错:', error)
      throw error
    }
  }

  // 获取指定发货的所有明细
  async findAllDetailsByTransportationId(
    transportationId: string
  ): Promise<TransportationDetail[]> {
    return this.transportationDetailModel
      .find({ transportation_id: transportationId })
      .sort({ series_number: 1 })
      .exec()
  }

  // 获取指定服装的发货明细
  async findAllDetailsByClothingId(clothingId: string): Promise<any[]> {
    // 使用聚合查询关联发货主表和明细表
    const details = await this.transportationDetailModel.aggregate([
      // 匹配指定的服装ID
      { $match: { clothing_id: clothingId } },
      // 关联发货主表
      {
        $lookup: {
          from: 'transportation', // 发货主表集合名
          localField: 'transportation_id',
          foreignField: '_id',
          as: 'transportation',
        },
      },
      // 展开发货主表数组（因为lookup返回的是数组）
      { $unwind: '$transportation' },
      // 投影需要的字段
      {
        $project: {
          _id: 1,
          transportation_id: 1,
          series_number: 1,
          clothing_name: 1,
          package_quantity: 1,
          QUP: 1,
          out_pcs: 1,
          oem: 1,
          clothing_id: 1,
          style: 1,
          createTime: 1,
          'transportation.date_out': 1,
          'transportation.supplier': 1,
          'transportation.transportation_id': 1,
          'transportation.remark': 1,
        },
      },
      // 按发货日期倒序排序
      { $sort: { 'transportation.date_out': -1 } },
    ])

    return details
  }

  // 获取单个发货明细
  async findOneDetail(id: string): Promise<TransportationDetail> {
    const detail = await this.transportationDetailModel.findById(id).exec()
    if (!detail) {
      throw new NotFoundException(`发货明细ID ${id} 不存在`)
    }
    return detail
  }

  // 更新发货明细
  async updateDetail(
    id: string,
    updateTransportationDetailDto: UpdateTransportationDetailDto
  ): Promise<TransportationDetail> {
    try {
      // 获取原始明细记录
      const originalDetail = await this.transportationDetailModel.findById(id).exec()
      if (!originalDetail) {
        throw new NotFoundException(`发货明细ID ${id} 不存在`)
      }

      // 更新明细
      const updatedDetail = await this.transportationDetailModel
        .findByIdAndUpdate(id, updateTransportationDetailDto, { new: true })
        .exec()

      if (!updatedDetail) {
        throw new NotFoundException(`更新发货明细失败，ID ${id} 不存在`)
      }

      // 如果服装ID或OEM状态发生变化，需要更新两个服装的发货数量
      if (
        originalDetail.clothing_id !== updatedDetail.clothing_id ||
        originalDetail.oem !== updatedDetail.oem
      ) {
        // 更新原始服装的发货数量
        await this.updateClothingShipments(originalDetail.clothing_id, originalDetail.oem)
        // 更新新服装的发货数量
        await this.updateClothingShipments(updatedDetail.clothing_id, updatedDetail.oem)
      } else if (originalDetail.out_pcs !== updatedDetail.out_pcs) {
        // 如果只是数量变化，只需更新当前服装的发货数量
        await this.updateClothingShipments(updatedDetail.clothing_id, updatedDetail.oem)
      }

      // 更新发货信息的总数量
      if (originalDetail.out_pcs !== updatedDetail.out_pcs) {
        await this.updateTransportationTotals(updatedDetail.transportation_id.toString())
      }

      return updatedDetail
    } catch (error) {
      console.error('更新发货明细时出错:', error)
      throw error
    }
  }

  // 删除发货明细
  async removeDetail(id: string): Promise<{ success: boolean; message: string }> {
    try {
      // 获取明细记录
      const detail = await this.transportationDetailModel.findById(id).exec()
      if (!detail) {
        throw new NotFoundException(`发货明细ID ${id} 不存在`)
      }

      // 删除明细
      await this.transportationDetailModel.findByIdAndDelete(id).exec()

      // 保存服装ID，以便在发生错误时仍能更新发货数量
      const clothingId = detail.clothing_id
      const oem = detail.oem
      const transportationId = detail.transportation_id.toString()

      // 更新服装的发货数量
      await this.updateClothingShipments(clothingId, oem)

      // 更新发货信息的总数量
      await this.updateTransportationTotals(transportationId)

      return {
        success: true,
        message: '发货明细已成功删除',
      }
    } catch (error) {
      console.error('删除发货明细时出错:', error)
      throw error
    }
  }

  // 获取年份选项列表
  async getYearOptions(suppliers?: string): Promise<string[]> {
    const filter: any = {}

    // 如果提供了供应商过滤条件
    if (suppliers) {
      const supplierArray = suppliers.split(',')
      if (supplierArray.length > 0) {
        filter.supplier = { $in: supplierArray }
      }
    }

    const years = await this.transportationModel.distinct('transportation_year', filter).exec()

    // 按年份降序排序
    return years.sort((a, b) => b.localeCompare(a))
  }

  // 获取供应商选项列表
  async getSupplierOptions(years?: string): Promise<string[]> {
    const filter: any = {}

    // 如果提供了年份过滤条件
    if (years) {
      const yearArray = years.split(',')
      if (yearArray.length > 0) {
        filter.transportation_year = { $in: yearArray }
      }
    }

    return this.transportationModel.distinct('supplier', filter).exec()
  }

  // 获取货运公司列表（为微信小程序提供）
  async getCompanyList(): Promise<{ code: number; data: string[]; message: string }> {
    try {
      const companies = await this.transportationModel.distinct('transportation_company').exec()
      return {
        code: 200,
        data: companies.filter(company => company), // 过滤掉空值
        message: '获取成功'
      }
    } catch (error) {
      console.error('获取货运公司列表失败:', error)
      return {
        code: 500,
        data: [],
        message: '获取失败'
      }
    }
  }

  // 更新到货日期（为微信小程序提供）
  async updateArrivedDate(id: string, arrived_date: string): Promise<{ code: number; message: string }> {
    try {
      const updatedTransportation = await this.transportationModel
        .findByIdAndUpdate(id, { date_arrived: new Date(arrived_date) }, { new: true })
        .exec()

      if (!updatedTransportation) {
        return {
          code: 404,
          message: '发货信息不存在'
        }
      }

      return {
        code: 200,
        message: '更新成功'
      }
    } catch (error) {
      console.error('更新到货日期失败:', error)
      return {
        code: 500,
        message: '更新失败'
      }
    }
  }

  // 获取最新的发货编码
  async getLatestTransportationId(): Promise<string> {
    try {
      // 查询最新的发货信息，按照发货编码倒序排序
      const latestTransportation = await this.transportationModel
        .findOne()
        .sort({ transportation_id: -1 })
        .select('transportation_id')
        .exec()

      if (latestTransportation && latestTransportation.transportation_id) {
        console.log('找到最新的发货编码:', latestTransportation.transportation_id)
        return latestTransportation.transportation_id
      } else {
        // 如果没有找到任何发货信息，返回默认值
        const currentYear = new Date().getFullYear()
        const defaultId = `FH${currentYear}001`
        console.log('没有找到发货信息，返回默认编码:', defaultId)
        return defaultId
      }
    } catch (error) {
      console.error('获取最新发货编码时出错:', error)
      // 出错时返回默认值
      const currentYear = new Date().getFullYear()
      return `FH${currentYear}001`
    }
  }

  // 导出选中发货明细
  async exportSelectedTransportationDetails(transportationIds: string[]): Promise<any> {
    try {
      console.log('导出选中发货明细，发货编码:', transportationIds)

      // 存储普通服装和OEM服装的编码数组
      const normalClothingIds: string[] = []
      const oemClothingIds: string[] = []

      // 存储所有发货明细，按发货编码顺序
      const allDetails: any[] = []

      // 1. 按发货编码的顺序，查询发货明细
      for (const transportationId of transportationIds) {
        // 查询发货信息
        const transportation = await this.transportationModel.findById(transportationId).exec()
        if (!transportation) {
          console.warn(`发货信息ID ${transportationId} 不存在，跳过`)
          continue
        }

        // 查询该发货的所有明细
        const details = await this.transportationDetailModel
          .find({ transportation_id: transportationId })
          .sort({ series_number: 1 })
          .exec()

        // 将发货日期添加到每个明细中
        const detailsWithDate = details.map(detail => ({
          ...detail.toObject(),
          date_out: transportation.date_out
        }))

        // 添加到总明细列表
        allDetails.push(...detailsWithDate)

        // 2. 将服装编码按是否为OEM分类存入两个数组，若已存在则跳过
        for (const detail of details) {
          if (detail.oem === '是') {
            if (!oemClothingIds.includes(detail.clothing_id)) {
              oemClothingIds.push(detail.clothing_id)
            }
          } else {
            if (!normalClothingIds.includes(detail.clothing_id)) {
              normalClothingIds.push(detail.clothing_id)
            }
          }
        }
      }

      // 3. 根据服装编码查询服装信息
      const normalClothingInfo = await Promise.all(
        normalClothingIds.map(async (clothingId) => {
          const clothing = await this.clothingModel.findOne({ clothing_id: clothingId }).exec()
          return clothing ? {
            clothing_id: clothingId,
            clothing_name: clothing.clothing_name,
            clipping_pcs: clothing.clipping_pcs || 0,
            shipments: clothing.shipments || 0,
            oem: '否'
          } : null
        })
      )

      const oemClothingInfo = await Promise.all(
        oemClothingIds.map(async (clothingId) => {
          const clothing = await this.oemClothingModel.findOne({ oem_clothing_id: clothingId }).exec()
          return clothing ? {
            clothing_id: clothingId,
            clothing_name: clothing.oem_clothing_name,
            in_pcs: clothing.in_pcs || 0,
            shipments: clothing.shipments || 0,
            oem: '是'
          } : null
        })
      )

      // 合并服装信息，过滤掉null值
      const clothingInfo = [
        ...normalClothingInfo.filter(item => item !== null),
        ...oemClothingInfo.filter(item => item !== null)
      ]

      // 按发货日期排序明细
      allDetails.sort((a, b) => {
        return new Date(a.date_out).getTime() - new Date(b.date_out).getTime()
      })

      return {
        details: allDetails,
        clothingInfo: clothingInfo
      }
    } catch (error) {
      console.error('导出选中发货明细时出错:', error)
      throw error
    }
  }

  // 更新服装的发货数量
  private async updateClothingShipments(clothingId: string, oem?: string): Promise<void> {
    try {
      // 根据OEM状态决定更新哪个集合
      if (oem === '是') {
        // 更新OEM服装的发货数量
        const result = await this.transportationDetailModel
          .aggregate([
            { $match: { clothing_id: clothingId, oem: '是' } },
            { $group: { _id: null, total_pcs: { $sum: '$out_pcs' } } },
          ])
          .exec()

        const totalPcs = result.length > 0 ? result[0].total_pcs : 0

        await this.oemClothingModel
          .updateOne({ oem_clothing_id: clothingId }, { $set: { shipments: totalPcs } })
          .exec()

        console.log(`已更新OEM服装 ${clothingId} 的发货数量为 ${totalPcs}`)
      } else {
        // 更新普通服装的发货数量
        const result = await this.transportationDetailModel
          .aggregate([
            { $match: { clothing_id: clothingId, $or: [{ oem: { $ne: '是' } }, { oem: null }] } },
            { $group: { _id: null, total_pcs: { $sum: '$out_pcs' } } },
          ])
          .exec()

        const totalPcs = result.length > 0 ? result[0].total_pcs : 0

        await this.clothingModel
          .updateOne({ clothing_id: clothingId }, { $set: { shipments: totalPcs } })
          .exec()

        console.log(`已更新服装 ${clothingId} 的发货数量为 ${totalPcs}`)
      }
    } catch (error) {
      console.error(`更新服装 ${clothingId} 的发货数量时出错:`, error)
      // 继续处理，不中断整个过程
    }
  }

  // 更新发货信息的总数量
  private async updateTransportationTotals(transportationId: string): Promise<void> {
    try {
      // 计算总件数
      const result = await this.transportationDetailModel
        .aggregate([
          { $match: { transportation_id: transportationId } },
          {
            $group: {
              _id: null,
              total_pcs: { $sum: '$out_pcs' },
              total_package_quantity: { $sum: '$package_quantity' },
            },
          },
        ])
        .exec()

      if (result.length > 0) {
        const { total_pcs, total_package_quantity } = result[0]

        // 更新发货信息
        await this.transportationModel
          .updateOne(
            { _id: transportationId },
            {
              $set: {
                total_pcs,
                total_package_quantity,
                remark: `总数：${total_pcs}`,
              },
            }
          )
          .exec()

        console.log(
          `已更新发货信息 ${transportationId} 的总数量为 ${total_pcs}，总包裹数为 ${total_package_quantity}`
        )
      }
    } catch (error) {
      console.error(`更新发货信息 ${transportationId} 的总数量时出错:`, error)
      // 继续处理，不中断整个过程
    }
  }
}
