# JY-MOSCOW 微信小程序

基于 JY-MONGO 后端重写的微信小程序项目。

## 项目结构

```
JY-MOSCOW/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── pages/                # 页面目录
│   ├── login/           # 登录页面
│   ├── transportations/ # 货运管理页面
│   └── clothing/        # 服装管理页面
├── utils/               # 工具函数
│   ├── http.js         # HTTP 请求封装
│   └── api.js          # API 接口定义
├── images/             # 图片资源
└── project.config.json # 项目配置文件
```

## 主要功能

### 1. 用户认证
- 微信授权登录
- JWT Token 管理
- 自动登录检测

### 2. 货运管理
- 货运单列表查看
- 按货运公司和到货状态筛选
- 更新到货日期
- 货运单详情查看

### 3. 服装管理
- 普通服装和 OEM 服装搜索
- 按年份筛选
- 服装详情查看
- 图片预览
- 价格修改（仅 OEM 服装）

## 技术特点

### 1. 新的后端集成
- 使用 JY-MONGO 后端的 JWT 认证系统
- 专门的 miniProgram 模块提供 API 服务
- 统一的错误处理和响应格式

### 2. 改进的用户体验
- 加载状态提示
- 错误处理和用户反馈
- 响应式设计
- 图片预览功能

### 3. 代码优化
- 模块化的 API 管理
- 统一的 HTTP 请求封装
- 错误边界处理
- 代码注释和文档

## 配置说明

### 后端配置
确保 JY-MONGO 后端项目的 `.env` 文件包含以下配置：

```env
# 微信小程序配置
WECHAT_APPID=your_wechat_appid
WECHAT_SECRET=your_wechat_secret

# JWT 配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d
```

### 小程序配置
在 `project.config.json` 中配置正确的 `appid`：

```json
{
  "appid": "your_wechat_appid"
}
```

### API 地址配置
在 `utils/http.js` 中配置后端 API 地址：

```javascript
const pubUrl = "http://localhost:4000/api"; // 开发环境
// const pubUrl = "https://your-production-domain.com/api"; // 生产环境
```

## 开发指南

### 1. 启动后端服务
```bash
cd JY-MONGO
pnpm install
pnpm run dev
```

### 2. 导入小程序项目
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择 JY-MOSCOW 文件夹
4. 输入正确的 AppID

### 3. 调试说明
- 确保后端服务正常运行
- 检查网络请求是否正常
- 查看控制台日志排查问题

## API 接口

### 认证相关
- `POST /api/miniprogram/openid` - 获取微信 OpenID
- `POST /api/miniprogram/login` - 用户登录
- `POST /api/miniprogram/operate` - 添加新用户

### 货运相关
- `GET /api/miniprogram/transportationList` - 获取货运单列表
- `GET /api/miniprogram/transportationCompanyList` - 获取货运公司列表
- `POST /api/miniprogram/updateArrivedDate` - 更新到货日期

### 服装相关
- `GET /api/miniprogram/clothingInfo` - 获取普通服装信息
- `GET /api/miniprogram/OemClothingInfo` - 获取 OEM 服装信息
- `GET /api/miniprogram/searchClothing` - 搜索普通服装
- `GET /api/miniprogram/searchOemClothing` - 搜索 OEM 服装
- `POST /api/miniprogram/changePrice` - 修改价格
- `GET /api/miniprogram/clothingImgList` - 获取服装图片列表
- `GET /api/miniprogram/oemClothingImgList` - 获取 OEM 服装图片列表

## 注意事项

1. **网络配置**：确保在微信开发者工具中配置了正确的服务器域名
2. **权限管理**：所有业务接口都需要 JWT Token 认证
3. **错误处理**：接口调用失败时会自动显示错误提示
4. **数据格式**：所有日期数据使用 YYYY-MM-DD 格式
5. **图片处理**：图片预览使用微信小程序原生 API

## 更新日志

### v2.0.0 (当前版本)
- 重写整个项目架构
- 集成 JY-MONGO 后端 JWT 认证
- 新增专门的 miniProgram API 模块
- 优化用户界面和交互体验
- 改进错误处理和加载状态
- 统一代码风格和注释
