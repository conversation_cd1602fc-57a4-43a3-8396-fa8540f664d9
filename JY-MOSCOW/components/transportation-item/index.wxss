/* components/transportation-item/index.wxss */
.container {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fff;
}

.index-number {
  width: 60rpx;
  height: 60rpx;
  background-color: #356363;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.item-list {
  flex: 1;
  margin-right: 20rpx;
}

.item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.item:last-child {
  border-bottom: none;
}

.clothing-name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.clothing-name.has-image {
  color: #07c160;
  font-weight: bold;
}

.out-pcs {
  font-size: 26rpx;
  color: #666;
  font-weight: bold;
}

.package-info {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  min-width: 120rpx;
  flex-shrink: 0;
}
