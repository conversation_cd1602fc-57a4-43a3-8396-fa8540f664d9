// 新版本API地址 - 指向 JY-MONGO 后端
const pubUrl = "http://localhost:4000/api"; // 开发环境
// const pubUrl = "https://your-production-domain.com/api"; // 生产环境

const http = (options) => {
  const token = wx.getStorageSync("tokenKey");

  return new Promise((resolve, reject) => {
    wx.request({
      url: pubUrl + options.url,
      method: options.method || "get",
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        Authorization: token ? "Bearer " + token : "",
      },
      success: (res) => {
        // 处理响应数据
        if (res.statusCode === 200) {
          resolve(res);
        } else if (res.statusCode === 401) {
          // 未授权，清除token并跳转到登录页
          wx.removeStorageSync("tokenKey");
          wx.showToast({
            title: "登录已过期，请重新登录",
            icon: "none",
          });
          wx.redirectTo({
            url: "/pages/login/login",
          });
          reject(res);
        } else {
          reject(res);
        }
      },
      fail: (error) => {
        console.error("请求失败:", error);
        wx.showToast({
          title: "网络请求失败",
          icon: "none",
        });
        reject(error);
      },
    });
  });
};

export default http;
