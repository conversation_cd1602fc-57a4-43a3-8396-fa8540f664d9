/* 搜索区域 */
.search-container {
  background-color: #fff;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.year-selector {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.year-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.year-picker {
  padding: 10rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
}

.search-box {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.search-btn {
  padding: 20rpx 30rpx;
  background-color: #356363;
  color: #fff;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.search-btn[disabled] {
  background-color: #ccc;
}

.clear-btn {
  text-align: center;
  padding: 10rpx;
  color: #999;
  font-size: 24rpx;
}

/* 分组标题 */
.section {
  margin-bottom: 40rpx;
}

.section-title {
  padding: 20rpx 30rpx;
  background-color: #f5f5f5;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  border-left: 8rpx solid #356363;
}

/* 服装项 */
.clothing-item {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.oem-item {
  border-left: 8rpx solid #ff6b6b;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.clothing-info {
  flex: 1;
}

.clothing-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.clothing-code {
  font-size: 24rpx;
  color: #666;
}

.price-container {
  display: flex;
  align-items: center;
}

.price {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: bold;
  margin-right: 20rpx;
}

.price-edit-btn {
  padding: 10rpx 20rpx;
  background-color: #356363;
  color: #fff;
  border-radius: 10rpx;
  font-size: 24rpx;
  border: none;
}

/* 进度条 */
.progress-container {
  margin-bottom: 20rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #333;
}

.progress-percent {
  font-size: 24rpx;
  color: #666;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #356363 0%, #4a7c7c 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

/* 详情信息 */
.clothing-details {
  margin-bottom: 20rpx;
}

.detail-row {
  display: flex;
  margin-bottom: 10rpx;
}

.label {
  font-size: 26rpx;
  color: #666;
  width: 160rpx;
}

.value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

/* 图片展示 */
.image-container {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.clothing-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  background-color: #f5f5f5;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.empty-hint {
  font-size: 24rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60rpx;
  color: #999;
  font-size: 28rpx;
}
