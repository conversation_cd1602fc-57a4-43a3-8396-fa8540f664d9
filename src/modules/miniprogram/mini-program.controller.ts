import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger'
import { MiniProgramService } from './mini-program.service'
import { ClothingService } from '../clothing/clothing.service'
import { OemClothingService } from '../oemClothing/oem-clothing.service'
import { TransportationService } from '../transportation/transportation.service'
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard'
import { MiniProgramLoginDto, GetOpenIdDto, AddUserDto } from './dto'

@ApiTags('微信小程序')
@Controller('miniprogram')
export class MiniProgramController {
  constructor(
    private readonly miniProgramService: MiniProgramService,
    private readonly clothingService: ClothingService,
    private readonly oemClothingService: OemClothingService,
    private readonly transportationService: TransportationService
  ) {}

  @Post('openid')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '获取微信OpenID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getOpenId(@Body() getOpenIdDto: GetOpenIdDto) {
    return this.miniProgramService.getOpenId(getOpenIdDto)
  }

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '微信小程序登录' })
  @ApiResponse({ status: 200, description: '登录成功' })
  @ApiResponse({ status: 401, description: '登录失败' })
  async login(@Body() loginDto: MiniProgramLoginDto) {
    return this.miniProgramService.login(loginDto)
  }

  @Post('operate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '添加新用户' })
  @ApiResponse({ status: 200, description: '用户创建成功' })
  @ApiResponse({ status: 409, description: '用户已存在' })
  async addNewUser(@Body() addUserDto: AddUserDto) {
    return this.miniProgramService.addNewUser(addUserDto)
  }

  @Get('transportationList')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取货运单列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getTransportationList(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('transportation_company') transportation_company?: string,
    @Query('transportation_date') transportation_date?: string
  ) {
    const queryParams = {
      page: parseInt(page),
      limit: parseInt(limit),
      transportation_company,
      transportation_date
    }
    return this.transportationService.findAll(queryParams)
  }

  @Get('transportationCompanyList')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取货运公司列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getTransportationCompanyList() {
    // 获取所有不重复的货运公司
    return this.transportationService.getCompanyList()
  }

  @Post('updateArrivedDate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新到货日期' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateArrivedDate(@Body() updateData: { id: string; arrived_date: string }) {
    return this.transportationService.updateArrivedDate(updateData.id, updateData.arrived_date)
  }

  @Get('clothingInfo')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取服装信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getClothingInfo(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('clothing_name') clothing_name?: string,
    @Query('clothing_year') clothing_year?: string
  ) {
    const queryParams = {
      page: parseInt(page),
      limit: parseInt(limit),
      clothing_name,
      clothing_year
    }
    return this.clothingService.findAll(queryParams)
  }

  @Get('OemClothingInfo')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取OEM服装信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getOemClothingInfo(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('oem_clothing_name') oem_clothing_name?: string,
    @Query('oem_clothing_year') oem_clothing_year?: string
  ) {
    const queryParams = {
      page: parseInt(page),
      limit: parseInt(limit),
      oem_clothing_name,
      oem_clothing_year
    }
    return this.oemClothingService.findAll(queryParams)
  }

  @Get('searchClothing')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '搜索普通服装' })
  @ApiResponse({ status: 200, description: '搜索成功' })
  async searchClothing(
    @Query('clothing_name') clothing_name?: string,
    @Query('clothing_code') clothing_code?: string
  ) {
    const queryParams = { clothing_name, clothing_code }
    return this.clothingService.findAll(queryParams)
  }

  @Get('searchOemClothing')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '搜索OEM服装' })
  @ApiResponse({ status: 200, description: '搜索成功' })
  async searchOemClothing(
    @Query('oem_clothing_name') oem_clothing_name?: string,
    @Query('oem_clothing_code') oem_clothing_code?: string
  ) {
    const queryParams = { oem_clothing_name, oem_clothing_code }
    return this.oemClothingService.findAll(queryParams)
  }

  @Post('changePrice')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '修改价格' })
  @ApiResponse({ status: 200, description: '修改成功' })
  async changePrice(@Body() priceData: { 
    id: string; 
    type: 'clothing' | 'oemClothing'; 
    price: number 
  }) {
    const { id, type, price } = priceData
    
    if (type === 'clothing') {
      return this.clothingService.update(id, { clothing_price: price })
    } else if (type === 'oemClothing') {
      return this.oemClothingService.update(id, { oem_clothing_price: price })
    }
    
    throw new Error('无效的类型')
  }

  @Get('oemClothingImgList')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取OEM服装图片列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getOemClothingImgList(@Query('oem_clothing_id') oem_clothing_id: string) {
    const oemClothing = await this.oemClothingService.findOne(oem_clothing_id)
    return {
      code: 200,
      data: oemClothing.oem_clothing_img || [],
      message: '获取成功'
    }
  }

  @Get('clothingImgList')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取普通服装图片列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getClothingImgList(@Query('clothing_id') clothing_id: string) {
    const clothing = await this.clothingService.findOne(clothing_id)
    return {
      code: 200,
      data: clothing.clothing_img || [],
      message: '获取成功'
    }
  }
}
