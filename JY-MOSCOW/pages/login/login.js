// pages/login/login.js
import Api from "../../utils/api.js"; // 引入封装好的API文件

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userName: "admin",
    userPwd: "",
    wxNickName: "",
    isLogin: true,
    wxOpenId: "",
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    try {
      // 获取微信OpenID
      const res = await this.getOpenid();
      console.log("获取到的OpenID:", res);
      if (res.data && res.data.code === 200) {
        const openid = res.data.data.openid;
        this.setData({
          wxOpenId: openid,
        });

        // 尝试通过OpenID自动登录
        const loginParams = {
          wxOpenId: openid
        };
        
        const loginRes = await Api.login(loginParams);
        
        if (loginRes.data && loginRes.data.code === 200) {
          // 自动登录成功
          wx.setStorageSync("tokenKey", loginRes.data.data.token);
          wx.switchTab({
            url: "/pages/transportations/transportations",
          });
        } else {
          // 需要手动登录
          this.setData({
            isLogin: false,
          });
        }
      } else {
        console.error("获取OpenID失败:", res);
        this.setData({
          isLogin: false,
        });
      }
    } catch (error) {
      console.error("初始化登录失败:", error);
      this.setData({
        isLogin: false,
      });
    }
  },

  /**
   * 获取微信OpenID
   */
  async getOpenid() {
    try {
      const { code } = await wx.login();
      const params = {
        code: code
      };
      return await Api.getOpenId(params);
    } catch (error) {
      console.error("获取微信授权码失败:", error);
      throw error;
    }
  },

  /**
   * 登录处理
   */
  async onLogin() {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    try {
      const params = {
        userName: this.data.userName,
        userPwd: this.data.userPwd,
        wxOpenId: this.data.wxOpenId,
        wxNickName: this.data.wxNickName,
      };

      // 如果第一次登录，没有注册，则点击登录的时候获取昵称和openID保存到后端
      if (this.data.wxNickName === "") {
        wx.showToast({
          title: "请点击获取昵称",
          icon: "none",
        });
        this.setData({ loading: false });
        return;
      }

      const loginRes = await Api.login(params);
      
      if (loginRes.data && loginRes.data.code === 200) {
        // 登录成功，保存token
        wx.setStorageSync("tokenKey", loginRes.data.data.token);
        
        // 如果是新用户，保存用户信息到后端
        const addUserParams = {
          userName: this.data.wxNickName,
          userPwd: this.data.userPwd,
          wxOpenId: this.data.wxOpenId,
          wxNickName: this.data.wxNickName,
          action: "add"
        };
        
        try {
          await Api.addNewUser(addUserParams);
        } catch (addUserError) {
          // 用户可能已存在，忽略错误
          console.log("用户可能已存在:", addUserError);
        }
        
        // 跳转到主页
        wx.switchTab({
          url: "/pages/transportations/transportations",
        });
      } else {
        wx.showToast({
          title: loginRes.data?.message || "登录失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("登录失败:", error);
      wx.showToast({
        title: "登录失败，请重试",
        icon: "none",
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 输入微信昵称
   */
  inpuWxNickName(e) {
    this.setData({
      wxNickName: e.detail.value,
    });
  },

  /**
   * 输入用户名
   */
  inputUserName(e) {
    this.setData({
      userName: e.detail.value,
    });
  },

  /**
   * 输入密码
   */
  inputPassword(e) {
    this.setData({
      userPwd: e.detail.value,
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});
